import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  CalendarIcon,
  ArrowLeft,
  ArrowRight,
  Check,
  MapPin,
  Building,
  FileText,
  Tag,
  Calendar,
  Image as ImageIcon,
  AlertCircle
} from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useRolePermissions } from '@/hooks/useRolePermissions';
import notificationService from '@/services/notificationService';
import { useTasks } from '@/hooks/use-tasks';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { supabase } from '@/integrations/supabase/client';
import LocationSearch from '@/components/ui/location-search';
import { getCoordinates } from '@/utils/location-utils';
import { FileUpload } from '@/components/ui/file-upload';
import PWAMobileLayout from '@/components/pwa/PWAMobileLayout';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { Progress } from '@/components/ui/progress';

import { CREATION_CATEGORIES } from '@/constants/categories';

// Step titles for the optimized 3-step form
const steps = [
  { id: 'details', title: 'Task Details' },
  { id: 'location', title: 'Location & Images' },
  { id: 'review', title: 'Review & Submit' }
];

const PWAPostTask: React.FC = () => {
  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [location, setLocation] = useState('');
  const [building, setBuilding] = useState('');
  const [room, setRoom] = useState('');
  const [category, setCategory] = useState('');
  const [dueDate, setDueDate] = useState<Date | undefined>(undefined);
  const [organizationLocation, setOrganizationLocation] = useState('');
  const [isLoadingOrgLocation, setIsLoadingOrgLocation] = useState(false);
  const [locationCoordinates, setLocationCoordinates] = useState<{lat: number, lng: number} | null>(null);
  const [useOrgLocation, setUseOrgLocation] = useState(true);
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);

  // Multi-step form state
  const [currentStep, setCurrentStep] = useState(0);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);

  const { toast } = useToast();
  const { user, isSchool, profile, organizationId } = useAuth();
  const { hasPermission } = useRolePermissions();
  const { createTask, isCreatingTask } = useTasks();
  const navigate = useNavigate();

  // Fetch organization location when component mounts
  useEffect(() => {
    if (organizationId) {
      fetchOrganizationLocation(organizationId);
    }
  }, [organizationId]);

  // Function to fetch organization location
  const fetchOrganizationLocation = async (orgId: string) => {
    setIsLoadingOrgLocation(true);
    try {
      const { data, error } = await supabase
        .from('organizations')
        .select('location_formatted, location_lat, location_lng, address')
        .eq('id', orgId)
        .single();

      if (error) {
        console.error('Error fetching organization location:', error);
        return;
      }

      if (data) {
        // Use formatted location if available, otherwise use address
        const locationText = data.location_formatted || data.address || '';
        setOrganizationLocation(locationText);

        // If using organization location, set it as the task location
        if (useOrgLocation && locationText) {
          setLocation(locationText);

          // Store coordinates if available
          if (data.location_lat && data.location_lng) {
            setLocationCoordinates({
              lat: data.location_lat,
              lng: data.location_lng
            });
          }
        }
      }
    } catch (error) {
      console.error('Error in fetchOrganizationLocation:', error);
    } finally {
      setIsLoadingOrgLocation(false);
    }
  };

  // Validate the current step
  const validateStep = (): boolean => {
    const errors: Record<string, string> = {};

    switch (currentStep) {
      case 0: // Task Details
        if (!title.trim()) errors.title = "Title is required";
        if (!description.trim()) errors.description = "Description is required";
        if (!category) errors.category = "Category is required";
        if (!dueDate) errors.dueDate = "Due date is required";
        break;
      case 1: // Location & Images
        if (!location.trim()) errors.location = "Location is required";
        break;
      // No validation for review step
      default:
        break;
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle next step
  const handleNextStep = () => {
    if (validateStep()) {
      setCurrentStep(prev => Math.min(prev + 1, steps.length - 1));
    }
  };

  // Handle previous step
  const handlePrevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0));
  };

  // Handle form submission
  const handleSubmit = () => {
    // Final validation of all required fields
    if (!title || !description || !location || !category || !dueDate) {
      toast({
        variant: "destructive",
        title: "Missing information",
        description: "Please fill in all required fields.",
      });
      return;
    }

    if (!user) {
      toast({
        variant: "destructive",
        title: "Authentication required",
        description: "Please log in to post a task.",
      });
      return;
    }

    if (!isSchool || !hasPermission('create_tasks')) {
      toast({
        variant: "destructive",
        title: "Permission denied",
        description: "You don't have permission to create tasks.",
      });
      return;
    }

    // Create task in database
    const taskData = {
      title,
      description,
      location,
      building,
      room,
      category,
      budget: 0, // Default budget is 0, will be set by admin when making public
      due_date: dueDate.toISOString(),
      // Add location coordinates if available
      ...(locationCoordinates ? {
        location_lat: locationCoordinates.lat,
        location_lng: locationCoordinates.lng,
        location_formatted: location
      } : {}),
      // Add organization ID for reference
      organization_id: organizationId,
      // Add uploaded images if any
      ...(uploadedImages.length > 0 ? { images: uploadedImages } : {})
    };

    // All tasks start with admin visibility and open status
    // Admin will review and assign them later
    createTask(taskData, {
      onSuccess: (data) => {
        console.log('PWA: Task creation onSuccess called with data:', data);
        console.log('PWA: Current user:', user);
        console.log('PWA: Current profile:', profile);

        // Create notification for task creation (for the creator)
        if (user && data && data.id) {
          console.log('PWA: Creating task update notification for creator');
          notificationService.createTaskUpdateNotification(
            user.id,
            data.id,
            title,
            'created',
            false // Disable email temporarily due to CORS issues
          );

          // Notify all admin users in the organization about the new task awaiting review
          console.log('PWA: Checking profile organization_id:', profile?.organization_id);
          if (profile?.organization_id) {
            const creatorName = profile.first_name && profile.last_name
              ? `${profile.first_name} ${profile.last_name}`
              : profile.first_name || 'A user';

            console.log('PWA: About to notify admins:', {
              organizationId: profile.organization_id,
              taskId: data.id,
              taskTitle: title,
              creatorName
            });

            notificationService.notifyAdminsOfNewTask(
              profile.organization_id,
              data.id,
              title,
              creatorName,
              false // Disable email temporarily due to CORS issues
            ).then(result => {
              console.log('PWA: Admin notification result:', result);
            }).catch(error => {
              console.error('PWA: Admin notification error:', error);
            });
          } else {
            console.error('PWA: No organization_id found in profile:', profile);
          }
        } else {
          console.error('PWA: Missing required data for notifications:', { user, data, dataId: data?.id });
        }

        // Reset the form
        setTitle('');
        setDescription('');
        setLocation('');
        setBuilding('');
        setRoom('');
        setCategory('');
        setDueDate(undefined);
        setUploadedImages([]);
        setCurrentStep(0);

        // Show success message
        toast({
          title: "Task created successfully",
          description: "Your task has been created and is pending review by an administrator.",
        });

        // Redirect to dashboard instead of tasks page
        // This ensures teachers can see their pending tasks in the dashboard
        navigate('/dashboard');
      }
    });
  };

  // If user is not logged in or doesn't have permission, show access denied
  if (!user || !isSchool || !hasPermission('create_tasks')) {
    return (
      <PWAMobileLayout>
        <div className="p-4">
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>
              {!user ? "Authentication Required" : "Access Denied"}
            </AlertTitle>
            <AlertDescription>
              {!user
                ? "Please log in to post a task."
                : "You don't have permission to create tasks. Only teachers, admins, maintenance, and support staff can create tasks."}
            </AlertDescription>
          </Alert>
          <Button
            className="w-full mt-4"
            onClick={() => navigate(-1)}
          >
            Go Back
          </Button>
        </div>
      </PWAMobileLayout>
    );
  }

  // Render the current step
  const renderStep = () => {
    switch (currentStep) {
      case 0: // Task Details (Combined: Title, Description, Category, Due Date)
        return (
          <div className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="title">Task Title <span className="text-red-500">*</span></Label>
              <Input
                id="title"
                placeholder="e.g. School Playground Equipment Repair"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className={formErrors.title ? "border-red-500" : ""}
              />
              {formErrors.title && (
                <p className="text-red-500 text-xs mt-1">{formErrors.title}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description <span className="text-red-500">*</span></Label>
              <Textarea
                id="description"
                placeholder="Provide a detailed description of the task..."
                rows={4}
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className={formErrors.description ? "border-red-500" : ""}
              />
              {formErrors.description && (
                <p className="text-red-500 text-xs mt-1">{formErrors.description}</p>
              )}
              <p className="text-sm text-gray-500">
                Include all details suppliers will need to know.
              </p>
            </div>

            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Category <span className="text-red-500">*</span></Label>
                <Select
                  value={category}
                  onValueChange={setCategory}
                >
                  <SelectTrigger
                    id="category"
                    className={formErrors.category ? "border-red-500" : ""}
                  >
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {CREATION_CATEGORIES.map((cat) => (
                      <SelectItem key={cat} value={cat}>
                        {cat}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {formErrors.category && (
                  <p className="text-red-500 text-xs mt-1">{formErrors.category}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="due-date">Due Date <span className="text-red-500">*</span></Label>
                <Popover open={isDatePickerOpen} onOpenChange={setIsDatePickerOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      id="due-date"
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !dueDate && "text-muted-foreground",
                        formErrors.dueDate && "border-red-500"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dueDate ? format(dueDate, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      mode="single"
                      selected={dueDate}
                      onSelect={(date) => {
                        setDueDate(date);
                        setIsDatePickerOpen(false); // Close the popover immediately after selection
                      }}
                      initialFocus
                      disabled={(date) => {
                        const today = new Date();
                        today.setHours(0, 0, 0, 0); // Set to start of today
                        return date < today;
                      }}
                    />
                  </PopoverContent>
                </Popover>
                {formErrors.dueDate && (
                  <p className="text-red-500 text-xs mt-1">{formErrors.dueDate}</p>
                )}
              </div>
            </div>
          </div>
        );

      case 1: // Location & Images (Combined: Address, Building, Room, Images)
        return (
          <div className="space-y-6">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label htmlFor="location">School Address <span className="text-red-500">*</span></Label>
                {organizationLocation && (
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="use-org-location"
                      checked={useOrgLocation}
                      onCheckedChange={(checked) => {
                        setUseOrgLocation(checked);
                        if (checked) {
                          // Use organization location
                          setLocation(organizationLocation);
                          // Fetch coordinates if needed
                          if (!locationCoordinates && organizationLocation) {
                            getCoordinates(organizationLocation).then(coords => {
                              if (coords) {
                                setLocationCoordinates(coords);
                              }
                            });
                          }
                        } else {
                          // Clear location if unchecked
                          setLocation('');
                          setLocationCoordinates(null);
                        }
                      }}
                    />
                    <Label htmlFor="use-org-location" className="text-xs">
                      Use organization address
                    </Label>
                  </div>
                )}
              </div>

              <div className="relative">
                <LocationSearch
                  value={location}
                  onChange={async (newLocation) => {
                    setLocation(newLocation);
                    setUseOrgLocation(newLocation === organizationLocation);

                    // Get coordinates for the new location
                    if (newLocation && newLocation !== organizationLocation) {
                      try {
                        const coords = await getCoordinates(newLocation);
                        if (coords) {
                          setLocationCoordinates(coords);
                        }
                      } catch (error) {
                        console.error('Error getting coordinates:', error);
                      }
                    }
                  }}
                  placeholder="Enter school address"
                  label=""
                  className={formErrors.location ? "border-red-500" : ""}
                />
                {formErrors.location && (
                  <p className="text-red-500 text-xs mt-1">{formErrors.location}</p>
                )}

                {isLoadingOrgLocation && (
                  <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                    <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-opacity-50 border-t-transparent rounded-full"></div>
                  </div>
                )}
              </div>

              {organizationLocation && (
                <p className="text-xs text-gray-500">
                  <MapPin className="inline-block h-3 w-3 mr-1" />
                  Organization address: {organizationLocation}
                </p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="building">Building</Label>
                <Input
                  id="building"
                  placeholder="e.g. Main Building"
                  value={building}
                  onChange={(e) => setBuilding(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="room">Room</Label>
                <Input
                  id="room"
                  placeholder="e.g. Room 101"
                  value={room}
                  onChange={(e) => setRoom(e.target.value)}
                />
              </div>
            </div>

            {/* Images Section */}
            <div className="space-y-2">
              <Label>Task Images (Optional)</Label>
              <FileUpload
                onImagesUploaded={(urls) => {
                  setUploadedImages(prev => [...prev, ...urls]);
                }}
                maxFiles={5}
              />
              <p className="text-sm text-gray-500">
                Upload up to 5 images to help describe the task.
              </p>
            </div>

            {uploadedImages.length > 0 && (
              <div className="mt-4">
                <Label>Uploaded Images ({uploadedImages.length})</Label>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {uploadedImages.map((url, index) => (
                    <div key={index} className="relative rounded-md overflow-hidden border border-gray-200">
                      <img
                        src={url}
                        alt={`Uploaded image ${index + 1}`}
                        className="w-full h-20 object-cover"
                      />
                      <Button
                        variant="destructive"
                        size="icon"
                        className="absolute top-1 right-1 h-5 w-5 rounded-full"
                        onClick={() => {
                          setUploadedImages(prev => prev.filter((_, i) => i !== index));
                        }}
                      >
                        <AlertCircle className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        );

      case 2: // Review & Submit
        return (
          <div className="space-y-6">
            <Alert className="bg-blue-50 border-blue-200">
              <AlertTitle className="text-blue-800">Task Review Process</AlertTitle>
              <AlertDescription className="text-blue-700">
                Your task will be reviewed by an administrator who will either assign it to internal staff or make it available to external suppliers.
              </AlertDescription>
            </Alert>

            <div className="space-y-4">
              <div className="border-b pb-2">
                <h3 className="text-sm font-medium text-gray-500">Basic Information</h3>
                <p className="text-base font-semibold mt-1">{title}</p>
                <p className="text-sm mt-1">{description}</p>
              </div>

              <div className="border-b pb-2">
                <h3 className="text-sm font-medium text-gray-500">Location</h3>
                <p className="text-base font-semibold mt-1">{location}</p>
                {(building || room) && (
                  <p className="text-sm mt-1">
                    {building && `Building: ${building}`}
                    {building && room && ' • '}
                    {room && `Room: ${room}`}
                  </p>
                )}
              </div>

              <div className="border-b pb-2">
                <h3 className="text-sm font-medium text-gray-500">Details</h3>
                <div className="flex justify-between mt-1">
                  <p className="text-base font-semibold">Category:</p>
                  <Badge variant="outline">{category}</Badge>
                </div>
                <div className="flex justify-between mt-1">
                  <p className="text-base font-semibold">Due Date:</p>
                  <p>{dueDate ? format(dueDate, "PPP") : 'Not set'}</p>
                </div>
              </div>

              {uploadedImages.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Images</h3>
                  <p className="text-sm mt-1">{uploadedImages.length} image(s) attached</p>
                </div>
              )}
            </div>

            <Button
              className="w-full bg-classtasker-blue hover:bg-blue-600"
              size="lg"
              disabled={isCreatingTask}
              onClick={handleSubmit}
            >
              {isCreatingTask ? "Posting..." : "Post Task for Review"}
            </Button>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <PWAMobileLayout>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="bg-white border-b px-4 py-3 flex items-center">
          <Button variant="ghost" size="icon" onClick={() => navigate(-1)} className="mr-2">
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-lg font-semibold">Post a Task</h1>
        </div>

        {/* Progress indicator */}
        <div className="px-4 py-3 bg-white border-b">
          <div className="flex justify-between mb-2">
            <span className="text-sm font-medium">Step {currentStep + 1} of {steps.length}</span>
            <span className="text-sm text-gray-500">{steps[currentStep].title}</span>
          </div>
          <Progress value={((currentStep + 1) / steps.length) * 100} className="h-1" />
        </div>

        {/* Form content */}
        <div className="flex-1 overflow-auto p-4">
          {renderStep()}
        </div>

        {/* Navigation buttons */}
        <div className="bg-white border-t px-4 py-3 flex justify-between">
          <Button
            variant="outline"
            onClick={handlePrevStep}
            disabled={currentStep === 0}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>

          {currentStep < steps.length - 1 ? (
            <Button onClick={handleNextStep}>
              Next
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          ) : (
            <Button
              variant="default"
              onClick={handleSubmit}
              disabled={isCreatingTask}
            >
              {isCreatingTask ? "Posting..." : "Submit"}
              <Check className="h-4 w-4 ml-2" />
            </Button>
          )}
        </div>
      </div>
    </PWAMobileLayout>
  );
};

export default PWAPostTask;
